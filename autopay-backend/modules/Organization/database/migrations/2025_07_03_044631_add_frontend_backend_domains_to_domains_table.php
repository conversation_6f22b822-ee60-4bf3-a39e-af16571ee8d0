<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('domains', function (Blueprint $table) {
            // Add separate frontend and backend domain fields
            $table->string('frontend_hostname')->nullable()->after('hostname');
            $table->string('backend_hostname')->nullable()->after('frontend_hostname');

            // Add indexes for performance
            $table->index(['frontend_hostname']);
            $table->index(['backend_hostname']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('domains', function (Blueprint $table) {
            $table->dropIndex(['frontend_hostname']);
            $table->dropIndex(['backend_hostname']);
            $table->dropColumn(['frontend_hostname', 'backend_hostname']);
        });
    }
};
