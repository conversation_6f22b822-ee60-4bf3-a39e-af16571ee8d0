<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domains', static function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('organization_id')->nullable()->constrained('organizations')->cascadeOnDelete();

            // Domain information
            $table->string('frontend_hostname')->nullable(); // Frontend domain mapping
            $table->string('backend_hostname')->nullable(); // Backend domain mapping
            $table->string('status')->default('active'); // 'pending', 'active', 'failed', 'suspended'

            // Branding configuration
            $table->string('brand_name')->nullable(); // Custom brand name
            $table->string('slogan')->nullable(); // Brand slogan
            $table->string('logo_url')->nullable(); // Logo image URL
            $table->string('favicon_url')->nullable(); // Favicon URL

            // Theme configuration
            $table->json('theme_colors')->nullable(); // Primary, secondary, accent colors
            $table->json('custom_css')->nullable(); // Custom CSS overrides

            // SEO configuration
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->string('og_image_url')->nullable(); // Open Graph image

            // Contact information
            $table->json('contact_info')->nullable(); // Phone, email, address, etc.

            // Custom configuration
            $table->json('custom_config')->nullable(); // Additional custom configurations

            // Status and settings
            $table->boolean('is_active')->default(true);

            $table->timestamps();

            // Indexes for performance
            $table->index(['frontend_hostname']);
            $table->index(['backend_hostname']);
            $table->index(['organization_id']);
            $table->index(['is_active']);
            $table->index(['status']);
            $table->index(['organization_id', 'status']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domains');
    }
};
