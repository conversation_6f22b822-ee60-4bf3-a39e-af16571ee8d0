<?php

namespace Modules\Organization\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Domain extends Model
{
    use HasFactory, HasUlids;

    protected $fillable = [
        'organization_id',
        'frontend_hostname',
        'backend_hostname',
        'brand_name',
        'slogan',
        'logo_url',
        'favicon_url',
        'theme_colors',
        'custom_css',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_image_url',
        'contact_info',
        'custom_config',
        'is_active',
        'status',
    ];

    protected $casts = [
        'theme_colors' => 'array',
        'custom_css' => 'array',
        'contact_info' => 'array',
        'custom_config' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the organization that owns the domain.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }



    /**
     * Scope to get active domains.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }



    /**
     * Get domain by backend hostname.
     */
    public function scopeByHostname($query, string $hostname)
    {
        return $query->where('backend_hostname', $hostname);
    }





    /**
     * Scope to get domains by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get domains by organization.
     */
    public function scopeByOrganization($query, string $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Get the full theme configuration with defaults.
     */
    public function getThemeConfigAttribute(): array
    {
        $defaultTheme = [
            'primary' => '#3b82f6',
            'secondary' => '#64748b',
            'accent' => '#f59e0b',
            'background' => '#ffffff',
            'surface' => '#f8fafc',
            'text' => '#1e293b',
            'text_secondary' => '#64748b',
        ];

        return array_merge($defaultTheme, $this->theme_colors ?? []);
    }

    /**
     * Get the full branding configuration.
     */
    public function getBrandingConfigAttribute(): array
    {
        return [
            'name' => $this->brand_name ?? $this->organization->name ?? 'Autopay',
            'slogan' => $this->slogan,
            'logo_url' => $this->logo_url,
            'favicon_url' => $this->favicon_url,
        ];
    }

    /**
     * Get the SEO configuration.
     */
    public function getSeoConfigAttribute(): array
    {
        return [
            'title' => $this->meta_title ?? $this->brand_name ?? $this->organization->name ?? 'Autopay',
            'description' => $this->meta_description,
            'keywords' => $this->meta_keywords,
            'og_image' => $this->og_image_url,
        ];
    }



    /**
     * Get the full domain configuration for frontend.
     */
    public function getConfigAttribute(): array
    {
        return [
            'id' => $this->id,
            'frontend_hostname' => $this->frontend_hostname,
            'backend_hostname' => $this->backend_hostname,
            'branding' => $this->branding_config,
            'theme' => $this->theme_config,
            'seo' => $this->seo_config,
            'contact' => $this->contact_info ?? [],
            'custom' => $this->custom_config ?? [],
            'custom_css' => $this->custom_css ?? [],
        ];
    }






    /**
     * Check if domain is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->is_active;
    }



}
