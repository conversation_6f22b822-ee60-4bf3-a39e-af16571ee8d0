<?php

namespace Modules\Organization\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Organization\Models\Domain;
use Modules\Organization\Http\Requests\StoreDomainRequest;
use Modules\Organization\Http\Requests\UpdateDomainRequest;
use Modules\Organization\Http\Requests\SetupDomainRequest;
use Modules\Organization\Traits\OrganizationFromRequest;

use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class DomainController extends Controller
{
    use OrganizationFromRequest;

    /**
     * Get domain configuration by hostname.
     */
    public function getConfig(Request $request): Response
    {
        $hostname = $request->get('hostname', $request->getHost());

        $cacheKey = "domain_config_{$hostname}";

        $config = Cache::remember($cacheKey, 3600, function () use ($hostname) {
            $domain = Domain::byHostname($hostname)->active()->first();

            return $domain?->config;
        });

        if (!$config) {
            return ResponseHelper::error('Không tìm thấy cấu hình domain', null, 404);
        }

        return ResponseHelper::success(data: $config);
    }

    /**
     * Get the domain for the organization (since each org has only one domain).
     */
    public function index(Request $request): Response
    {
        $organization = $this->getOrganizationFromRequest($request);

        if (!$organization) {
            return ResponseHelper::error('Không tìm thấy tổ chức', null, 404);
        }

        $domain = Domain::byOrganization($organization->id)
                        ->active()
                        ->first();

        if (!$domain) {
            return ResponseHelper::error('Tổ chức này chưa cấu hình domain', null, 404);
        }

        return ResponseHelper::success(data: $domain);
    }

    /**
     * Create a new domain.
     */
    public function store(StoreDomainRequest $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (!$organization) {
                return ResponseHelper::error('Không tìm thấy tổ chức', null, 404);
            }

            $data = $request->validated();

            // Automatically set organization_id from route
            $data['organization_id'] = $organization->id;

            // Check if organization already has a domain (limit 1 per organization)
            $existingDomain = Domain::where('organization_id', $organization->id)->first();
            if ($existingDomain) {
                return ResponseHelper::error('Tổ chức chỉ có thể có một domain. Vui lòng cập nhật domain hiện tại thay vì tạo mới.', null, 400);
            }

            // Auto-generate hostname and name from frontend_hostname or backend_hostname
            if (!empty($data['frontend_hostname'])) {
                $data['hostname'] = $data['frontend_hostname'];
                $data['name'] = 'Frontend Domain - ' . $data['frontend_hostname'];
            } elseif (!empty($data['backend_hostname'])) {
                $data['hostname'] = $data['backend_hostname'];
                $data['name'] = 'Backend Domain - ' . $data['backend_hostname'];
            } else {
                return ResponseHelper::error('Vui lòng nhập ít nhất một trong Frontend Domain hoặc Backend Domain', null, 422);
            }

            // Auto-generate description
            $data['description'] = 'Domain mapping cho tổ chức ' . $organization->name;

            // Determine domain type
            $appDomain = config('app.domain', 'autopay.vn');
            $data['domain_type'] = str_ends_with($data['hostname'], ".{$appDomain}") ? 'subdomain' : 'custom';
            $data['status'] = $data['domain_type'] === 'subdomain' ? 'active' : 'pending';
            $data['is_active'] = true;

            // Check hostname availability
            if (!$this->isHostnameAvailable($data['hostname'])) {
                throw ValidationException::withMessages([
                    'hostname' => ['Tên miền này đã được sử dụng.']
                ]);
            }

            $domain = Domain::create($data);

            // Clear cache
            $this->clearDomainCache($data['hostname']);

            return ResponseHelper::success('Tạo domain thành công', $domain, 201);
        } catch (ValidationException $e) {
            return ResponseHelper::error('Xác thực thất bại', $e->errors(), 422);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Update a domain.
     */
    public function update(UpdateDomainRequest $request, string $id): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (!$organization) {
                return ResponseHelper::error('Không tìm thấy tổ chức', null, 404);
            }

            $domain = Domain::where('id', $id)
                            ->where('organization_id', $organization->id)
                            ->first();

            if (!$domain) {
                return ResponseHelper::error('Không tìm thấy domain', null, 404);
            }

            $data = $request->validated();

            // Check hostname availability if hostname is being changed
            if (isset($data['hostname']) && $data['hostname'] !== $domain->hostname) {
                if (!$this->isHostnameAvailable($data['hostname'], $id)) {
                    throw ValidationException::withMessages([
                        'hostname' => ['Tên miền này đã được sử dụng.']
                    ]);
                }
            }

            $domain->update($data);

            // Clear cache for both old and new hostnames
            $this->clearDomainCache($domain->hostname);
            if (isset($data['hostname'])) {
                $this->clearDomainCache($data['hostname']);
            }

            return ResponseHelper::success('Cập nhật domain thành công');
        } catch (ValidationException $e) {
            return ResponseHelper::error('Xác thực thất bại', $e->errors(), 422);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Delete a domain.
     */
    public function destroy(string $id, Request $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (!$organization) {
                return ResponseHelper::error('Không tìm thấy tổ chức', null, 404);
            }

            $domain = Domain::where('id', $id)
                            ->where('organization_id', $organization->id)
                            ->first();

            if (!$domain) {
                return ResponseHelper::error('Không tìm thấy domain', null, 404);
            }

            $domain->delete();

            // Clear cache
            $this->clearDomainCache($domain->hostname);

            return ResponseHelper::success('Xóa domain thành công');
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Setup domain for organization.
     */
    public function setupDomain(SetupDomainRequest $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            if (!$organization) {
                return ResponseHelper::error('Không tìm thấy tổ chức', null, 404);
            }

            // Validate domain format
            $errors = $this->validateDomainFormat($request->hostname);
            if (!empty($errors)) {
                return ResponseHelper::error('Xác thực domain thất bại', $errors, 422);
            }

            $domain = $this->setupDomainForOrganization(
                $request->hostname,
                $organization->id,
                $request->only(['name', 'description'])
            );

            return ResponseHelper::success('Khởi tạo thiết lập domain thành công', $domain, 201);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Check if hostname is available.
     */
    private function isHostnameAvailable(string $hostname, ?string $excludeId = null): bool
    {
        $query = Domain::where('hostname', $hostname);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return !$query->exists();
    }

    /**
     * Clear domain cache.
     */
    private function clearDomainCache(string $hostname): void
    {
        Cache::forget("domain_config_{$hostname}");
    }

    /**
     * Validate domain format.
     */
    private function validateDomainFormat(string $hostname): array
    {
        $errors = [];

        // Basic hostname validation
        if (!filter_var($hostname, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME)) {
            $errors[] = 'Định dạng tên miền không hợp lệ';
        }

        // Check for reserved domains
        $reservedDomains = ['localhost', 'autopay.vn', 'www.autopay.vn'];
        if (in_array($hostname, $reservedDomains)) {
            $errors[] = 'Tên miền này đã được bảo lưu và không thể sử dụng';
        }

        return $errors;
    }

    /**
     * Setup domain for organization.
     */
    private function setupDomainForOrganization(string $hostname, string $organizationId, array $additionalData = []): Domain
    {
        // Check if organization already has a domain
        $existingDomain = Domain::where('organization_id', $organizationId)->first();
        if ($existingDomain) {
            throw new \Exception('Tổ chức đã có domain được cấu hình');
        }

        // Determine domain type
        $appDomain = config('app.domain', 'autopay.vn');
        $domainType = str_ends_with($hostname, ".{$appDomain}") ? 'subdomain' : 'custom';

        // Create domain
        $domainData = array_merge([
            'organization_id' => $organizationId,
            'hostname' => $hostname,
            'name' => $additionalData['name'] ?? $hostname,
            'description' => $additionalData['description'] ?? null,
            'domain_type' => $domainType,
            'status' => $domainType === 'subdomain' ? 'active' : 'pending',
            'is_active' => true,
            'force_https' => true,
        ], $additionalData);

        return Domain::create($domainData);
    }
}
