<?php

namespace Modules\Organization\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Modules\Organization\Models\Domain;
use Modules\Organization\Models\Organization;
use Symfony\Component\HttpFoundation\Response;

class ResolveDomainOrganization
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $hostname = $request->getHost();

        // Skip resolution for API routes or admin routes
        if ($this->shouldSkipResolution($request)) {
            return $next($request);
        }

        // Determine domain type first
        $domainType = $this->determineDomainType($hostname);
        $request->attributes->set('domain_type', $domainType);

        // Try to resolve domain and organization
        $domain = $this->resolveDomain($hostname);

        if ($domain && $domain->is_active) {
            // Set domain and organization in request
            $request->attributes->set('domain', $domain);
            $request->attributes->set('organization', $domain->organization);

            // Set organization context for the application
            app()->instance('current.domain', $domain);
            app()->instance('current.organization', $domain->organization);

            // Add domain configuration to view data
            if ($domain->organization) {
                view()->share('currentDomain', $domain);
                view()->share('currentOrganization', $domain->organization);
                view()->share('brandConfig', $this->getBrandConfig($domain));
            }

            // Set customer domain flag if applicable
            if ($domainType === 'customer') {
                $request->attributes->set('is_customer_domain', true);
            }
        } else if ($domainType === 'admin') {
            // For admin domains without specific domain configuration
            $request->attributes->set('is_admin_domain', true);
        }

        // Ensure current.organization is always bound, even if null
        if (!app()->bound('current.organization')) {
            app()->instance('current.organization', null);
        }

        return $next($request);
    }

    /**
     * Check if domain resolution should be skipped.
     */
    protected function shouldSkipResolution(Request $request): bool
    {
        $path = $request->path();

        // Skip for system routes
        $systemRoutes = [
            '_debugbar',
            'telescope',
            'horizon',
            'health',
            'up',
        ];

        foreach ($systemRoutes as $route) {
            if (str_starts_with($path, $route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine domain type based on hostname.
     */
    protected function determineDomainType(string $hostname): string
    {
        $adminDomains = [
            'admin.autopay.vn',
            'app.autopay.vn',
            'dashboard.autopay.vn',
            'localhost:8000', // for local development
            '127.0.0.1:8000', // for local development
        ];

        if (in_array($hostname, $adminDomains, true)) {
            return 'admin';
        }

        return 'customer';
    }

    /**
     * Resolve domain from hostname with caching.
     */
    protected function resolveDomain(string $hostname): ?Domain
    {
        $cacheKey = "domain.resolution.{$hostname}";

        return Cache::remember($cacheKey, 300, function () use ($hostname) {
            return Domain::with('organization')
                        ->where('hostname', $hostname)
                        ->where('is_active', true)
                        ->where('status', 'active')
                        ->first();
        });
    }

    /**
     * Get brand configuration for the domain.
     */
    protected function getBrandConfig(Domain $domain): array
    {
        return [
            'brand_name' => $domain->brand_name ?: $domain->organization->name,
            'slogan' => $domain->slogan,
            'logo_url' => $domain->logo_url,
            'favicon_url' => $domain->favicon_url,
            'theme_colors' => $domain->theme_colors ?: [
                'primary' => '#3b82f6',
                'secondary' => '#64748b',
                'accent' => '#f59e0b',
                'background' => '#ffffff',
                'foreground' => '#0f172a',
            ],
            'custom_css' => $domain->custom_css,
            'meta' => [
                'title' => $domain->meta_title ?: $domain->brand_name,
                'description' => $domain->meta_description,
                'keywords' => $domain->meta_keywords,
                'og_image' => $domain->og_image_url,
            ],
            'contact_info' => $domain->contact_info,
        ];
    }

    /**
     * Clear domain resolution cache.
     */
    public static function clearCache(string $hostname): void
    {
        Cache::forget("domain.resolution.{$hostname}");
    }

    /**
     * Clear all domain resolution caches.
     */
    public static function clearAllCache(): void
    {
        $domains = Domain::pluck('hostname');

        foreach ($domains as $hostname) {
            static::clearCache($hostname);
        }
    }
}
