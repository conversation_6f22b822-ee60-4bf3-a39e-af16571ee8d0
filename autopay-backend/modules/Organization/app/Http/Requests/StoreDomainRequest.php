<?php

namespace Modules\Organization\Http\Requests;

use Modules\Core\Http\Requests\BaseFormRequest;

class StoreDomainRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'hostname' => 'required|string|max:255',
            'frontend_hostname' => 'nullable|string|max:255',
            'backend_hostname' => 'nullable|string|max:255',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'brand_name' => 'nullable|string|max:255',
            'slogan' => 'nullable|string|max:500',
            'logo_url' => 'nullable|url',
            'favicon_url' => 'nullable|url',
            'theme_colors' => 'nullable|array',
            'custom_css' => 'nullable|array',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'og_image_url' => 'nullable|url',
            'contact_info' => 'nullable|array',
            'custom_config' => 'nullable|array',
            'is_active' => 'nullable|boolean',
            'organization_id' => 'nullable|exists:organizations,id',
        ];
    }
}
