<?php

namespace Modules\Customer\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Modules\Organization\Models\Domain;
use Symfony\Component\HttpFoundation\Response;

class SetCustomerGuard
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $hostname = $request->getHost();
        
        // Determine if this is an admin domain or customer domain
        if ($this->isAdminDomain($hostname)) {
            // Use default user authentication for admin domains
            Config::set('auth.defaults.guard', 'sanctum');
        } else {
            // Check if this is a customer domain
            $domain = app('current.domain');
            
            if ($domain && $this->isCustomerDomain($domain)) {
                // Use customer authentication for customer domains
                Config::set('auth.defaults.guard', 'customer');
                
                // Set the request attribute to indicate this is a customer domain
                $request->attributes->set('is_customer_domain', true);
                $request->attributes->set('domain_type', 'customer');
            }
        }

        return $next($request);
    }

    /**
     * Check if the hostname is an admin domain.
     */
    protected function isAdminDomain(string $hostname): bool
    {
        $adminDomains = [
            'admin.autopay.vn',
            'app.autopay.vn',
            'dashboard.autopay.vn',
            'localhost:8000', // for local development
            '127.0.0.1:8000', // for local development
        ];

        return in_array($hostname, $adminDomains, true);
    }

    /**
     * Check if the domain is configured for customer access.
     */
    protected function isCustomerDomain(Domain $domain): bool
    {
        // Check domain type or custom configuration
        return $domain->domain_type === 'customer' || 
               $domain->custom_config['customer_portal'] ?? true;
    }

    /**
     * Get the appropriate guard name based on domain type.
     */
    public static function getGuardForDomain(string $hostname): string
    {
        $middleware = new static();
        
        if ($middleware->isAdminDomain($hostname)) {
            return 'sanctum';
        }

        $domain = app('current.domain');
        if ($domain && $middleware->isCustomerDomain($domain)) {
            return 'customer';
        }

        // Default to user guard
        return 'sanctum';
    }

    /**
     * Check if current request is on a customer domain.
     */
    public static function isCurrentRequestCustomerDomain(Request $request): bool
    {
        return $request->attributes->get('is_customer_domain', false);
    }

    /**
     * Get domain type from request.
     */
    public static function getDomainType(Request $request): string
    {
        return $request->attributes->get('domain_type', 'admin');
    }
}
