<?php

namespace Modules\Customer\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Customer\Models\Customer;
use Modules\Organization\Models\Organization;
use Symfony\Component\HttpFoundation\Response;

class CustomerController extends Controller
{
    /**
     * Get customer dashboard data
     */
    public function dashboard(Request $request): Response
    {
        $customer = $request->user('customer');
        $organization = app('current.organization');

        if (!$customer || !$organization) {
            return ResponseHelper::error(
                'Không thể truy cập dashboard',
                httpCode: Response::HTTP_UNAUTHORIZED
            );
        }

        // Verify customer belongs to current organization
        if (!$customer->belongsToOrganization($organization)) {
            return ResponseHelper::error(
                'Khách hàng không thuộc tổ chức này',
                httpCode: Response::HTTP_FORBIDDEN
            );
        }

        $dashboardData = [
            'customer' => [
                'id' => $customer->id,
                'name' => $customer->name,
                'email' => $customer->email,
                'phone' => $customer->phone,
                'avatar' => $customer->avatar,
            ],
            'organization' => [
                'id' => $organization->id,
                'name' => $organization->name,
                'alias' => $organization->alias,
            ],
            'stats' => [
                // Add customer-specific statistics here
                'total_orders' => 0,
                'total_spent' => 0,
                'last_login' => $customer->updated_at,
            ],
        ];

        return ResponseHelper::success('Dashboard data', data: $dashboardData);
    }

    /**
     * Update customer profile
     */
    public function updateProfile(Request $request): Response
    {
        $customer = $request->user('customer');
        $organization = app('current.organization');

        if (!$customer || !$organization) {
            return ResponseHelper::error(
                'Không thể cập nhật profile',
                httpCode: Response::HTTP_UNAUTHORIZED
            );
        }

        $validatedData = $request->validate([
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'phone' => 'sometimes|nullable|string|max:20',
            'avatar' => 'sometimes|nullable|string|max:500',
        ]);

        // Update name if first_name or last_name changed
        if (isset($validatedData['first_name']) || isset($validatedData['last_name'])) {
            $firstName = $validatedData['first_name'] ?? $customer->first_name;
            $lastName = $validatedData['last_name'] ?? $customer->last_name;
            $validatedData['name'] = trim($firstName . ' ' . $lastName);
        }

        $customer->update($validatedData);

        return ResponseHelper::success('Cập nhật profile thành công!', data: [
            'customer' => [
                'id' => $customer->id,
                'name' => $customer->name,
                'first_name' => $customer->first_name,
                'last_name' => $customer->last_name,
                'email' => $customer->email,
                'phone' => $customer->phone,
                'avatar' => $customer->avatar,
            ],
        ]);
    }
}
