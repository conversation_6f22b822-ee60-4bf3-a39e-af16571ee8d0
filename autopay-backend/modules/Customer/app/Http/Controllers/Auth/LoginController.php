<?php

namespace Modules\Customer\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Customer\Http\Requests\Auth\LoginRequest;
use Modules\Customer\Models\Customer;
use Modules\Organization\Models\Organization;
use Symfony\Component\HttpFoundation\Response;

class LoginController extends Controller
{
    /**
     * Handle customer login request
     */
    public function login(LoginRequest $request): Response
    {
        // Get current organization from domain context
        $organization = app('current.organization');
        
        if (!$organization) {
            return ResponseHelper::error(
                'Không thể xác định tổ chức từ domain này',
                httpCode: Response::HTTP_BAD_REQUEST
            );
        }

        // Find customer in the current organization
        $customer = Customer::where('email', $request->email)
                           ->where('organization_id', $organization->id)
                           ->where('is_active', true)
                           ->first();

        if (!$customer || !Hash::check($request->password, $customer->password)) {
            throw ValidationException::withMessages([
                'email' => ['Thông tin đăng nhập không chính xác.'],
            ]);
        }

        // Create token for customer
        $deviceName = $request->userAgent() ?? 'Unknown Device';
        $token = $customer->createToken($deviceName, ['customer'])->plainTextToken;

        // Prepare customer data
        $customerData = [
            'id' => $customer->id,
            'name' => $customer->name,
            'first_name' => $customer->first_name,
            'last_name' => $customer->last_name,
            'email' => $customer->email,
            'phone' => $customer->phone,
            'avatar' => $customer->avatar,
            'email_verified_at' => $customer->email_verified_at,
            'organization_id' => $customer->organization_id,
            'organization' => [
                'id' => $organization->id,
                'name' => $organization->name,
                'alias' => $organization->alias,
            ],
        ];

        return ResponseHelper::success('Đăng nhập thành công!', data: [
            'access_token' => $token,
            'customer' => $customerData,
            'token_type' => 'Bearer',
        ]);
    }

    /**
     * Handle customer logout
     */
    public function logout(Request $request): Response
    {
        $customer = $request->user('customer');
        
        if ($customer) {
            // Revoke current token
            $customer->currentAccessToken()->delete();
        }

        return ResponseHelper::success('Đăng xuất thành công!');
    }

    /**
     * Get current customer info
     */
    public function me(Request $request): Response
    {
        $customer = $request->user('customer');
        $organization = app('current.organization');

        if (!$customer) {
            return ResponseHelper::error(
                'Không tìm thấy thông tin khách hàng',
                httpCode: Response::HTTP_UNAUTHORIZED
            );
        }

        $customerData = [
            'id' => $customer->id,
            'name' => $customer->name,
            'first_name' => $customer->first_name,
            'last_name' => $customer->last_name,
            'email' => $customer->email,
            'phone' => $customer->phone,
            'avatar' => $customer->avatar,
            'email_verified_at' => $customer->email_verified_at,
            'organization_id' => $customer->organization_id,
            'organization' => [
                'id' => $organization->id,
                'name' => $organization->name,
                'alias' => $organization->alias,
            ],
        ];

        return ResponseHelper::success('Thông tin khách hàng', data: [
            'customer' => $customerData,
        ]);
    }
}
