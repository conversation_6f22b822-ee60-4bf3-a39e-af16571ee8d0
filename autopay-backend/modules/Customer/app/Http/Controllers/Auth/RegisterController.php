<?php

namespace Modules\Customer\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Customer\Http\Requests\Auth\RegisterRequest;
use Modules\Customer\Models\Customer;
use Modules\Organization\Models\Organization;
use Symfony\Component\HttpFoundation\Response;

class RegisterController extends Controller
{
    /**
     * Handle customer registration request
     */
    public function register(RegisterRequest $request): Response
    {
        // Get current organization from domain context
        $organization = app('current.organization');
        
        if (!$organization) {
            return ResponseHelper::error(
                'Không thể xác định tổ chức từ domain này',
                httpCode: Response::HTTP_BAD_REQUEST
            );
        }

        $validatedData = $request->validated();

        // Combine first_name and last_name to create full name
        $fullName = trim($validatedData['first_name'] . ' ' . $validatedData['last_name']);

        // Create customer
        $customer = Customer::create([
            'organization_id' => $organization->id,
            'name' => $fullName,
            'first_name' => $validatedData['first_name'],
            'last_name' => $validatedData['last_name'],
            'email' => $validatedData['email'],
            'phone' => $validatedData['phone'] ?? null,
            'password' => Hash::make($validatedData['password']),
            'is_active' => true,
        ]);

        // Create token for customer
        $deviceName = $request->userAgent() ?? 'Unknown Device';
        $token = $customer->createToken($deviceName, ['customer'])->plainTextToken;

        // Prepare customer data
        $customerData = [
            'id' => $customer->id,
            'name' => $customer->name,
            'first_name' => $customer->first_name,
            'last_name' => $customer->last_name,
            'email' => $customer->email,
            'phone' => $customer->phone,
            'avatar' => $customer->avatar,
            'email_verified_at' => $customer->email_verified_at,
            'organization_id' => $customer->organization_id,
            'organization' => [
                'id' => $organization->id,
                'name' => $organization->name,
                'alias' => $organization->alias,
            ],
        ];

        return ResponseHelper::success('Đăng ký thành công!', data: [
            'access_token' => $token,
            'customer' => $customerData,
            'token_type' => 'Bearer',
        ], httpCode: Response::HTTP_CREATED);
    }
}
