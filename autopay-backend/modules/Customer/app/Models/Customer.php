<?php

namespace Modules\Customer\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Modules\Core\Models\Concerns\HasSchemalessAttributes;
use Modules\Organization\Models\Organization;

class Customer extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasSchemalessAttributes, HasUlids, Notifiable;

    protected $table = 'customers';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'organization_id',
        'name',
        'first_name',
        'last_name',
        'email',
        'phone',
        'password',
        'avatar',
        'email_verified_at',
        'is_active',
        'data',
    ];

    /**
     * The attributes that should be hidden for arrays.
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'is_active' => 'boolean',
            'data' => 'object',
        ];
    }

    /**
     * Get the organization that owns the customer.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the guard name for the customer.
     */
    public function getGuardName(): string
    {
        return 'customer';
    }

    /**
     * Check if customer belongs to specific organization.
     */
    public function belongsToOrganization(Organization $organization): bool
    {
        return $this->organization_id === $organization->id;
    }

    /**
     * Scope to filter customers by organization.
     */
    public function scopeForOrganization($query, Organization $organization)
    {
        return $query->where('organization_id', $organization->id);
    }

    /**
     * Scope to filter active customers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
