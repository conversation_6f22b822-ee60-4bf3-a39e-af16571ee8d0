<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Customer Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for customer functionality.
| These routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group.
|
*/

Route::group([
    'middleware' => ['customer.guard'],
], function () {
    // Customer web routes can be added here if needed
    // For now, we're focusing on API-based authentication
});
