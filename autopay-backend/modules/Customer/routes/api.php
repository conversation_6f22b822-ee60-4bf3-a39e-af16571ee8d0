<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON><PERSON>\Customer\Http\Controllers\Auth\LoginController;
use Modules\Customer\Http\Controllers\Auth\RegisterController;
use Modu<PERSON>\Customer\Http\Controllers\CustomerController;

/*
|--------------------------------------------------------------------------
| Customer API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for customer authentication
| and customer-specific functionality. These routes are loaded by the
| RouteServiceProvider within a group which is assigned the "api" middleware group.
|
*/

// Test route
Route::get('/customer/test', function () {
    $organization = null;
    try {
        $organization = app('current.organization');
    } catch (\Exception $e) {
        // Organization not bound
    }

    return response()->json([
        'message' => 'Customer routes working',
        'organization' => $organization?->name ?? 'No organization',
        'timestamp' => now()
    ]);
});

// Customer authentication routes (guest only)
Route::group([
    'prefix' => 'customer',
], function () {

    // Guest routes (no authentication required)
    Route::group([
        'middleware' => 'guest:customer',
    ], function () {
        Route::post('/login', [LoginController::class, 'login'])->name('customer.login');
        Route::post('/register', [RegisterController::class, 'register'])->name('customer.register');
    });

    // Authenticated customer routes
    Route::group([
        'middleware' => 'auth:customer',
    ], function () {
        Route::post('/logout', [LoginController::class, 'logout'])->name('customer.logout');
        Route::get('/me', [LoginController::class, 'me'])->name('customer.me');

        // Customer dashboard and profile
        Route::get('/dashboard', [CustomerController::class, 'dashboard'])->name('customer.dashboard');
        Route::put('/profile', [CustomerController::class, 'updateProfile'])->name('customer.profile.update');
    });
});
