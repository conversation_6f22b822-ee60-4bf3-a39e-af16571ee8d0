<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON><PERSON>\Customer\Http\Controllers\Auth\LoginController;
use Modules\Customer\Http\Controllers\Auth\RegisterController;
use Modules\Customer\Http\Controllers\CustomerController;

/*
|--------------------------------------------------------------------------
| Customer API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for customer authentication
| and customer-specific functionality. These routes are loaded by the
| RouteServiceProvider within a group which is assigned the "api" middleware group.
|
*/

// Test route
Route::get('/customer/test', function () {
    $organization = null;
    try {
        $organization = app('current.organization');
    } catch (\Exception $e) {
        // Organization not bound
    }

    return response()->json([
        'message' => 'Customer routes working',
        'organization' => $organization?->name ?? 'No organization',
        'timestamp' => now()
    ]);
});

// Simple register test
Route::post('/customer/register-test', function (\Illuminate\Http\Request $request) {
    $organization = app('current.organization');

    return response()->json([
        'message' => 'Register test',
        'organization' => $organization?->name ?? 'No organization',
        'data' => $request->all()
    ]);
});

// Register without FormRequest validation
Route::post('/customer/register-simple', function (\Illuminate\Http\Request $request) {
    $organization = app('current.organization');

    if (!$organization) {
        return response()->json(['error' => 'No organization found'], 400);
    }

    // Simple validation
    $request->validate([
        'first_name' => 'required|string|max:255',
        'last_name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'password' => 'required|string|min:6',
    ]);

    // Create customer
    $customer = \Modules\Customer\Models\Customer::create([
        'organization_id' => $organization->id,
        'first_name' => $request->first_name,
        'last_name' => $request->last_name,
        'email' => $request->email,
        'phone' => $request->phone,
        'password' => \Illuminate\Support\Facades\Hash::make($request->password),
        'is_active' => true,
    ]);

    return response()->json([
        'success' => true,
        'message' => 'Customer created successfully',
        'customer' => $customer
    ]);
});

// Register with FormRequest but no guest middleware
Route::post('/customer/register-no-middleware', [\Modules\Customer\Http\Controllers\Auth\RegisterController::class, 'register']);

// Test auth
Route::get('/customer/auth-test', function () {
    return response()->json([
        'message' => 'Auth test',
        'user' => auth('customer')->user(),
        'guard' => auth()->getDefaultDriver(),
        'authenticated' => auth('customer')->check()
    ]);
})->middleware('auth:customer');

// Customer authentication routes (guest only)
Route::group([
    'prefix' => 'customer',
], function () {

    // Guest routes (no authentication required)
    Route::group([
        'middleware' => 'guest:customer',
    ], function () {
        Route::post('/login', [LoginController::class, 'login'])->name('customer.login');
        Route::post('/register', [RegisterController::class, 'register'])->name('customer.register');
    });

    // Authenticated customer routes
    Route::group([
        'middleware' => 'auth:customer',
    ], function () {
        Route::post('/logout', [LoginController::class, 'logout'])->name('customer.logout');
        Route::get('/me', [LoginController::class, 'me'])->name('customer.me');

        // Customer dashboard and profile
        Route::get('/dashboard', [CustomerController::class, 'dashboard'])->name('customer.dashboard');
        Route::put('/profile', [CustomerController::class, 'updateProfile'])->name('customer.profile.update');
    });
});
