// Test script to verify API switching logic
console.log('🧪 Testing API Switching Logic...\n');

// Simulate the API helper functions
let globalApiUrl = null;

function setGlobalApiUrl(apiUrl) {
  globalApiUrl = apiUrl;
  console.log('🔄 API URL updated to:', apiUrl);
}

function getCurrentApiUrl() {
  return globalApiUrl || process.env.NEXT_PUBLIC_API_URL || 'https://api.autopay.id';
}

function resetApiUrl() {
  globalApiUrl = null;
  console.log('🔄 API URL reset to default:', getCurrentApiUrl());
}

// Simulate domain config
const mockDomainConfig = {
  id: "01jz7akbgvyhha663m86w0y8e5",
  frontend_hostname: "app.testcompany.com",
  backend_hostname: "api.testcompany.com",
  branding: {
    name: "Test Company"
  }
};

// Test scenarios
console.log('=== Test 1: Default API URL ===');
console.log('Current API URL:', getCurrentApiUrl());
console.log('Expected: https://api.autopay.id');
console.log('✅ Pass:', getCurrentApiUrl() === 'https://api.autopay.id');
console.log('');

console.log('=== Test 2: Switch to Custom API ===');
if (mockDomainConfig.backend_hostname) {
  setGlobalApiUrl(`https://${mockDomainConfig.backend_hostname}`);
}
console.log('Current API URL:', getCurrentApiUrl());
console.log('Expected: https://api.testcompany.com');
console.log('✅ Pass:', getCurrentApiUrl() === 'https://api.testcompany.com');
console.log('');

console.log('=== Test 3: Reset to Default ===');
resetApiUrl();
console.log('Current API URL:', getCurrentApiUrl());
console.log('Expected: https://api.autopay.id');
console.log('✅ Pass:', getCurrentApiUrl() === 'https://api.autopay.id');
console.log('');

console.log('=== Test 4: Simulate fetchHelper with custom API ===');
function simulateFetchHelper(url, options = {}) {
  const { customApiUrl, ...fetchOptions } = options;
  const baseApiUrl = customApiUrl || getCurrentApiUrl();
  const requestUrl = url.startsWith('http') ? url : baseApiUrl + url;
  
  console.log('Request URL:', requestUrl);
  return { requestUrl, options: fetchOptions };
}

// Test with default API
console.log('--- Default API ---');
const result1 = simulateFetchHelper('/profile');
console.log('Result:', result1.requestUrl);
console.log('Expected: https://api.autopay.id/profile');
console.log('✅ Pass:', result1.requestUrl === 'https://api.autopay.id/profile');
console.log('');

// Test with custom API
console.log('--- Custom API ---');
setGlobalApiUrl('https://api.testcompany.com');
const result2 = simulateFetchHelper('/profile');
console.log('Result:', result2.requestUrl);
console.log('Expected: https://api.testcompany.com/profile');
console.log('✅ Pass:', result2.requestUrl === 'https://api.testcompany.com/profile');
console.log('');

// Test with override
console.log('--- Override API ---');
const result3 = simulateFetchHelper('/profile', { customApiUrl: 'https://override.example.com' });
console.log('Result:', result3.requestUrl);
console.log('Expected: https://override.example.com/profile');
console.log('✅ Pass:', result3.requestUrl === 'https://override.example.com/profile');
console.log('');

console.log('🎉 All tests completed!');
