<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Switching Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 API Switching Test</h1>
        
        <div class="test-section">
            <h2>1. Test Domain Config API</h2>
            <button onclick="testDomainConfig()">Test Domain Config</button>
            <div id="domainResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>2. Test API URL Switching</h2>
            <p><strong>Default API:</strong> <span id="defaultApi">https://api.autopay.id</span></p>
            <p><strong>Current API:</strong> <span id="currentApi">https://api.autopay.id</span></p>
            <p><strong>Backend Hostname:</strong> <span id="backendHostname">Not loaded</span></p>
            
            <button onclick="loadDomainConfig()">Load Domain Config</button>
            <button onclick="switchToCustomApi()">Switch to Custom API</button>
            <button onclick="resetToDefaultApi()">Reset to Default</button>
            
            <div id="switchResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h2>3. Test API Calls</h2>
            <button onclick="testApiCall()">Test API Call</button>
            <div id="apiResult" class="result"></div>
        </div>
    </div>

    <script>
        let currentApiUrl = 'https://api.autopay.id';
        let domainConfig = null;
        
        function updateDisplay() {
            document.getElementById('currentApi').textContent = currentApiUrl;
            document.getElementById('backendHostname').textContent = 
                domainConfig?.backend_hostname || 'Not loaded';
        }
        
        async function testDomainConfig() {
            const resultDiv = document.getElementById('domainResult');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('https://api.autopay.id/domains/config?hostname=app.testcompany.com');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ Success!</strong><br>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                    `;
                    domainConfig = data.data;
                    updateDisplay();
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>❌ Error:</strong> ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
            }
        }
        
        async function loadDomainConfig() {
            await testDomainConfig();
        }
        
        function switchToCustomApi() {
            const resultDiv = document.getElementById('switchResult');
            
            if (!domainConfig?.backend_hostname) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ No backend hostname available. Load domain config first.';
                return;
            }
            
            currentApiUrl = `https://${domainConfig.backend_hostname}`;
            updateDisplay();
            
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `✅ Switched to custom API: ${currentApiUrl}`;
        }
        
        function resetToDefaultApi() {
            currentApiUrl = 'https://api.autopay.id';
            updateDisplay();
            
            const resultDiv = document.getElementById('switchResult');
            resultDiv.className = 'result success';
            resultDiv.innerHTML = '✅ Reset to default API';
        }
        
        async function testApiCall() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = 'Testing API call...';
            
            try {
                const response = await fetch(`${currentApiUrl}/domains/config?hostname=app.testcompany.com`);
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ API Call Success!</strong><br>
                        <strong>Used API:</strong> ${currentApiUrl}<br>
                        <strong>Response:</strong><br>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>❌ API Error:</strong> ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Network Error:</strong> ${error.message}`;
            }
        }
        
        // Initialize
        updateDisplay();
    </script>
</body>
</html>
