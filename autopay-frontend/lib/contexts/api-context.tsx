'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useDomain } from '../hooks/useDomain'

interface ApiContextType {
  apiUrl: string
  isUsingCustomDomain: boolean
  setCustomApiUrl: (url: string | null) => void
}

const ApiContext = createContext<ApiContextType | null>(null)

interface ApiProviderProps {
  children: React.ReactNode
}

export function ApiProvider({ children }: ApiProviderProps): React.JSX.Element {
  const { config: domainConfig } = useDomain()
  const [customApiUrl, setCustomApiUrl] = useState<string | null>(null)
  
  // Default API URL from environment
  const defaultApiUrl = process.env.NEXT_PUBLIC_API_URL || ''
  
  // Determine which API URL to use
  const apiUrl = customApiUrl || 
    (domainConfig?.backend_hostname ? `https://${domainConfig.backend_hostname}` : defaultApiUrl)
  
  const isUsingCustomDomain = !!domainConfig?.backend_hostname && !customApiUrl

  // Auto-update API URL when domain config changes
  useEffect(() => {
    if (domainConfig?.backend_hostname && !customApiUrl) {
      console.log('🔄 Switching to custom domain API:', `https://${domainConfig.backend_hostname}`)
    }
  }, [domainConfig?.backend_hostname, customApiUrl])

  const contextValue: ApiContextType = {
    apiUrl,
    isUsingCustomDomain,
    setCustomApiUrl,
  }

  return <ApiContext.Provider value={contextValue}>{children}</ApiContext.Provider>
}

export function useApi(): ApiContextType {
  const context = useContext(ApiContext)
  if (!context) {
    throw new Error('useApi must be used within an ApiProvider')
  }
  return context
}
