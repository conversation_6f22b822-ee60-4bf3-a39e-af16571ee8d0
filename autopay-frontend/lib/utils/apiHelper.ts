'use client'

import { fetchHelper, queryFetchHelper } from './fetchHelper'

// Global API URL state for client-side usage
let globalApiUrl: string | null = null

/**
 * Set the global API URL for client-side requests
 */
export function setGlobalApiUrl(apiUrl: string): void {
  globalApiUrl = apiUrl
  console.log('🔄 API URL updated to:', apiUrl)
}

/**
 * Get the current API URL
 */
export function getCurrentApiUrl(): string {
  return globalApiUrl || process.env.NEXT_PUBLIC_API_URL || ''
}

/**
 * Enhanced fetch helper that uses the global API URL
 */
export async function apiFetch(url: string, options: any = {}): Promise<ApiResponse> {
  const customApiUrl = globalApiUrl || undefined
  return fetchHelper(url, { ...options, customApiUrl })
}

/**
 * Enhanced query fetch helper that uses the global API URL
 */
export async function apiQueryFetch(url: string, options: any = {}): Promise<any> {
  const customApiUrl = globalApiUrl || undefined
  return queryFetchHelper(url, { ...options, customApiUrl })
}

/**
 * Reset API URL to default
 */
export function resetApiUrl(): void {
  globalApiUrl = null
  console.log('🔄 API URL reset to default:', process.env.NEXT_PUBLIC_API_URL)
}
