'use client'

import { apiFetch } from '@/lib/utils/apiHelper'
import { useState } from 'react'

export function ApiTest(): React.JSX.Element {
  const [testResult, setTestResult] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)

  const testApiCall = async (): Promise<void> => {
    setIsLoading(true)
    setTestResult('Testing...')
    
    try {
      const response = await apiFetch('/profile')
      setTestResult(`✅ Success: ${JSON.stringify(response, null, 2)}`)
    } catch (error: any) {
      setTestResult(`❌ Error: ${error.message || JSON.stringify(error, null, 2)}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="font-bold mb-2">🧪 API Test</h3>
      
      <button
        onClick={testApiCall}
        disabled={isLoading}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {isLoading ? 'Testing...' : 'Test API Call'}
      </button>
      
      {testResult && (
        <div className="mt-4 p-3 bg-white border rounded text-xs">
          <pre className="whitespace-pre-wrap">{testResult}</pre>
        </div>
      )}
    </div>
  )
}
